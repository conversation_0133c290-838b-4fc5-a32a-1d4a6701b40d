#pragma once

#include <fstream>
#include <iostream>
#include <sstream>
#include <opencv2/opencv.hpp>
#include "NvInfer.h"

using namespace nvinfer1;
using namespace cv;

struct DetectResult
{
	int classId;
	float conf;
	cv::Rect box;
	cv::Mat mask;  // 添加mask成员
};

class YOLOv8TRTSegment 
{
	public:
		YOLOv8TRTSegment(){} // 显示声明构造函数
		void initConfig(std::string enginefile, float conf_threshold,float score_threshold);
		void detect(cv::Mat &frame,std::vector<DetectResult>& results);
		~YOLOv8TRTSegment();
private:
    float sigmoid_function(float a);

    // 推理参数
    float conf_threshold = 0.25f;
    float score_threshold = 0.25f;
    int input_h = 640;
    int input_w = 640;
    int output_h = 0;
    int output_w = 0;

    // TensorRT核心对象
    IRuntime* runtime{nullptr};
    ICudaEngine* engine{nullptr};
    IExecutionContext* context{nullptr};
    void* buffers[3] = {nullptr, nullptr, nullptr};  // 输入输出缓冲区

    // CUDA流和数据缓存
    std::vector<float> prob;
    std::vector<float> mprob;
    cudaStream_t stream{nullptr};  // 也可以用 = nullptr
};

