
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
      鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:01銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:02.04
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-vkdyb9"
      binary: "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-vkdyb9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-vkdyb9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_01cdc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:04銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vkdyb9\\cmTC_01cdc.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_01cdc.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vkdyb9\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_01cdc.dir\\Debug\\cmTC_01cdc.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_01cdc.dir\\Debug\\cmTC_01cdc.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_01cdc.dir\\Debug\\cmTC_01cdc.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_01cdc.dir\\Debug\\\\" /Fd"cmTC_01cdc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.38.33145 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_01cdc.dir\\Debug\\\\" /Fd"cmTC_01cdc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vkdyb9\\Debug\\cmTC_01cdc.exe" /INCREMENTAL /ILK:"cmTC_01cdc.dir\\Debug\\cmTC_01cdc.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-vkdyb9/Debug/cmTC_01cdc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-vkdyb9/Debug/cmTC_01cdc.lib" /MACHINE:X64  /machine:x64 cmTC_01cdc.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_01cdc.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vkdyb9\\Debug\\cmTC_01cdc.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_01cdc.dir\\Debug\\cmTC_01cdc.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_01cdc.dir\\Debug\\cmTC_01cdc.tlog\\cmTC_01cdc.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vkdyb9\\cmTC_01cdc.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.51
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.38.33145.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:53 (__determine_compiler_id_test)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCUDACompiler.cmake:131 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:46 (enable_language)"
    message: |
      Compiling the CUDA compiler identification source file "CMakeCUDACompilerId.cu" succeeded.
      Compiler:  
      Build flags: 
      Id flags: --keep;--keep-dir;tmp -v
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
      鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:06銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCUDA.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      AddCudaCompileDeps:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe /E /nologo /showIncludes /TP /D__CUDACC__ /D__CUDACC_VER_MAJOR__=11 /D__CUDACC_VER_MINOR__=8 /D_MBCS /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I. /FIcuda_runtime.h /c E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu 
      椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?1)姝ｅ湪鑺傜偣 1 涓婄敓鎴愨€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?1:2) (CudaBuildCore 涓洰鏍?銆?
      CudaBuildCore:
        Compiling CUDA source file CMakeCUDACompilerId.cu...
        姝ｅ湪鍒涘缓鐩綍鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug鈥濄€?
        cmd.exe /C "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp8e13b2fa0d264f6185033debf32c38cd.cmd"
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe" -gencode=arch=compute_52,code=\\"sm_52,compute_52\\" --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu   -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"  -G   --keep-dir CompilerIdCUDA\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -v -allow-unsupported-compiler -g  -D_MBCS -Xcompiler "/EHsc /W0 /nologo /Od /FdDebug\\vc143.pdb /FS /Zi /RTC1 /MDd " -o E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu"
        
        E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA>"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe" -gencode=arch=compute_52,code=\\"sm_52,compute_52\\" --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu   -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"  -G   --keep-dir CompilerIdCUDA\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -v -allow-unsupported-compiler -g  -D_MBCS -Xcompiler "/EHsc /W0 /nologo /Od /FdDebug\\vc143.pdb /FS /Zi /RTC1 /MDd " -o E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" 
        #$ _NVVM_BRANCH_=nvvm
        #$ _SPACE_= 
        #$ _CUDART_=cudart
        #$ _HERE_=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin
        #$ _THERE_=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin
        #$ _TARGET_SIZE_=
        #$ _TARGET_DIR_=
        #$ _TARGET_SIZE_=64
        #$ _WIN_PLATFORM_=x64
        #$ TOP=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/..
        #$ NVVMIR_LIBRARY_DIR=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../nvvm/libdevice
        #$ PATH=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../nvvm/bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../lib;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x64;;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x86;;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\ide;C:\\Program Files (x86)\\HTML Help Workshop;;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64;C:\\WINDOWS\\Microsoft.NET\\Framework64\\v4.0.30319\\;C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin;C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\;;D:\\miniconda;D:\\miniconda\\Library\\mingw-w64\\bin;D:\\miniconda\\Library\\usr\\bin;D:\\miniconda\\Library\\bin;D:\\miniconda\\Scripts;D:\\miniconda\\bin;D:\\miniconda\\condabin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\libnvvp;C:\\Python312\\Scripts;C:\\Python312;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Program Files\\dotnet;C:\\Program Files\\TortoiseSVN\\bin;C:\\Windows\\System32;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\TensorRT-*******\\lib;C:\\Program Files\\Docker\\Docker\\resources\\bin;D:\\Erlang OTP\\bin;D:\\ffmpeg-7.0.2-full_build\\bin;D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\VisualSVN Server\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;D:\\nc\\netcat-win32-1.11\\netcat-1.11;D:\\WezTerm;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\AppData\\Local\\Programs\\oh-my-posh\\bin;C:\\Users\\<USER>\\scoop\\shims;C:\\Program Files (x86)\\Microsoft\\Edge\\Application;C:\\Python312\\Scripts;C:\\Python312;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\libnvvp;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\lib\\x64;C:\\Program Files\\dotnet;C:\\Program Files\\Git\\cmd;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Program Files\\TortoiseSVN\\bin;C:\\Windows\\System32;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\TensorRT-*******\\lib;C:\\Program Files\\Docker\\Docker\\resources\\bin;D:\\Erlang OTP\\bin;D:\\ffmpeg-7.0.2-full_build\\bin;D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\VisualSVN Server\\bin;\\bin\\x64-win64;C:\\Users\\<USER>\\AppData\\Local;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;D:\\cmake-3.31.6-windows-x86_64\\bin;C:\\Users\\<USER>\\xmake;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64;.;{03555C97-E719-40ed-B706-5CE607D0B817}_9832;.;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;
        #$ INCLUDES="-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../include"  
        #$ LIBRARIES=  "/LIBPATH:C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../lib/x64"
        #$ CUDAFE_FLAGS=--sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10"
        #$ PTXAS_FLAGS=
        CMakeCUDACompilerId.cu
        #$ cl.exe -D__NV_NO_HOST_COMPILER_CHECK=1 @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-9.res" > "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-8_CMakeCUDACompilerId.cpp1.ii" 
        CMakeCUDACompilerId.cu
        #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-9.res
        #$ cicc --microsoft_version=1938 --msvc_target_version=1938 --compiler_bindir "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/../../../../../../.." --display_error_number --orig_src_file_name "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --orig_src_path_name "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" --allow_managed --debug_mode --sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10"  -arch compute_52 -m64 --no-version-ident -ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 --include_file_name "tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin.c" -g -O0 -tused --gen_module_id_file --module_id_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-5_CMakeCUDACompilerId.module_id" --gen_c_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.c" --stub_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.stub.c" --gen_device_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.gpu"  "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-8_CMakeCUDACompilerId.cpp1.ii" -o "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx"
        #$ ptxas -arch=sm_52 -m64 -g --dont-merge-basicblocks --return-at-end "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx"  -o "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-10_CMakeCUDACompilerId.sm_52.cubin" 
        #$ fatbinary -64 --ident="E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --cmdline="-g --dont-merge-basicblocks --return-at-end " --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " -g "--image3=kind=elf,sm=52,file=C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-10_CMakeCUDACompilerId.sm_52.cubin" "--image3=kind=ptx,sm=52,file=C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx" --embedded-fatbin="C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin.c" 
        #$ erase C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin
        #$ cl.exe -D__NV_NO_HOST_COMPILER_CHECK=1 @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-11.res" > "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-6_CMakeCUDACompilerId.cpp4.ii" 
        CMakeCUDACompilerId.cu
        #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-11.res
        #$ cudafe++ --microsoft_version=1938 --msvc_target_version=1938 --compiler_bindir "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/../../../../../../.." --display_error_number --orig_src_file_name "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --orig_src_path_name "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" --allow_managed --debug_mode --sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10" --m64 --parse_templates --gen_c_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.cpp" --stub_file_name "tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.stub.c" --module_id_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-5_CMakeCUDACompilerId.module_id" "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-6_CMakeCUDACompilerId.cpp4.ii" 
        #$ cl.exe @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-12.res" -Fo"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CompilerIdCUDA/x64/Debug/CMakeCUDACompilerId.cu.obj" 
        tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.cpp
        #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-12.res
      宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?CudaBuildCore 涓洰鏍?鐨勬搷浣溿€?
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj
          姝ｅ湪鍒涘缓搴?.\\CompilerIdCUDA.lib 鍜屽璞?.\\CompilerIdCUDA.exp
      LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj]
        CompilerIdCUDA.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.exe
      PostBuildEvent:
        echo CMAKE_CUDA_COMPILER=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe
        :VCEnd
        CMAKE_CUDA_COMPILER=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCUDA.tlog\\CompilerIdCUDA.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
      
      鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj]
      
          1 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:04.36
      
      
      Compilation of the CUDA compiler identification source "CMakeCUDACompilerId.cu" produced "CompilerIdCUDA.exe"
      
      Compilation of the CUDA compiler identification source "CMakeCUDACompilerId.cu" produced "CompilerIdCUDA.exp"
      
      Compilation of the CUDA compiler identification source "CMakeCUDACompilerId.cu" produced "CompilerIdCUDA.lib"
      
      Compilation of the CUDA compiler identification source "CMakeCUDACompilerId.cu" produced "CompilerIdCUDA.vcxproj"
      
      The CUDA compiler identification is NVIDIA, found in:
        E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CompilerIdCUDA.exe
      The host compiler identification is MSVC
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/Internal/CMakeNVCCParseImplicitInfo.cmake:128 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCUDACompiler.cmake:242 (cmake_nvcc_parse_implicit_info)"
      - "CMakeLists.txt:46 (enable_language)"
    message: |
      Parsed CUDA nvcc implicit link information:
        found 'PATH=' string: [C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../nvvm/bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../lib;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x64;;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x86;;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\ide;C:\\Program Files (x86)\\HTML Help Workshop;;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64;C:\\WINDOWS\\Microsoft.NET\\Framework64\\v4.0.30319\\;C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin;C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\;;D:\\miniconda;D:\\miniconda\\Library\\mingw-w64\\bin;D:\\miniconda\\Library\\usr\\bin;D:\\miniconda\\Library\\bin;D:\\miniconda\\Scripts;D:\\miniconda\\bin;D:\\miniconda\\condabin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\libnvvp;C:\\Python312\\Scripts;C:\\Python312;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Program Files\\dotnet;C:\\Program Files\\TortoiseSVN\\bin;C:\\Windows\\System32;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\TensorRT-*******\\lib;C:\\Program Files\\Docker\\Docker\\resources\\bin;D:\\Erlang OTP\\bin;D:\\ffmpeg-7.0.2-full_build\\bin;D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\VisualSVN Server\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;D:\\nc\\netcat-win32-1.11\\netcat-1.11;D:\\WezTerm;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\AppData\\Local\\Programs\\oh-my-posh\\bin;C:\\Users\\<USER>\\scoop\\shims;C:\\Program Files (x86)\\Microsoft\\Edge\\Application;C:\\Python312\\Scripts;C:\\Python312;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\libnvvp;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\lib\\x64;C:\\Program Files\\dotnet;C:\\Program Files\\Git\\cmd;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Program Files\\TortoiseSVN\\bin;C:\\Windows\\System32;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\TensorRT-*******\\lib;C:\\Program Files\\Docker\\Docker\\resources\\bin;D:\\Erlang OTP\\bin;D:\\ffmpeg-7.0.2-full_build\\bin;D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\VisualSVN Server\\bin;\\bin\\x64-win64;C:\\Users\\<USER>\\AppData\\Local;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;D:\\cmake-3.31.6-windows-x86_64\\bin;C:\\Users\\<USER>\\xmake;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64;.;{03555C97-E719-40ed-B706-5CE607D0B817}_9832;.;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;]
        found 'LIBRARIES=' string: ["/LIBPATH:C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../lib/x64"]
        found 'INCLUDES=' string: ["-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../include"  ]
        considering line: [閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a]
        considering line: [鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:06銆?]
        considering line: [鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣)銆?]
        considering line: [PrepareForBuild:]
        considering line: [  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?]
        considering line: [  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?]
        considering line: [  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCUDA.tlog\\鈥濄€?]
        considering line: [InitializeBuildStatus:]
        considering line: [  姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?]
        considering line: [  姝ｅ湪瀵光€淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?]
        considering line: [AddCudaCompileDeps:]
        considering line: [  C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe /E /nologo /showIncludes /TP /D__CUDACC__ /D__CUDACC_VER_MAJOR__=11 /D__CUDACC_VER_MINOR__=8 /D_MBCS /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I. /FIcuda_runtime.h /c E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu ]
        considering line: [椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?1)姝ｅ湪鑺傜偣 1 涓婄敓鎴愨€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?1:2) (CudaBuildCore 涓洰鏍?銆?]
        considering line: [CudaBuildCore:]
        considering line: [  Compiling CUDA source file CMakeCUDACompilerId.cu...]
        considering line: [  姝ｅ湪鍒涘缓鐩綍鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug鈥濄€?]
        considering line: [  cmd.exe /C "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp8e13b2fa0d264f6185033debf32c38cd.cmd"]
        considering line: [  "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe" -gencode=arch=compute_52,code=\\"sm_52,compute_52\\" --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu   -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"  -G   --keep-dir CompilerIdCUDA\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -v -allow-unsupported-compiler -g  -D_MBCS -Xcompiler "/EHsc /W0 /nologo /Od /FdDebug\\vc143.pdb /FS /Zi /RTC1 /MDd " -o E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu"]
        considering line: [  ]
        considering line: [  E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA>"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe" -gencode=arch=compute_52,code=\\"sm_52,compute_52\\" --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu   -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"  -G   --keep-dir CompilerIdCUDA\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -v -allow-unsupported-compiler -g  -D_MBCS -Xcompiler "/EHsc /W0 /nologo /Od /FdDebug\\vc143.pdb /FS /Zi /RTC1 /MDd " -o E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" ]
        considering line: [                                  CMakeCUDACompilerId.cu]
        considering line: [  #$ cl.exe -D__NV_NO_HOST_COMPILER_CHECK=1 @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-9.res" > "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-8_CMakeCUDACompilerId.cpp1.ii" ]
        considering line: [  CMakeCUDACompilerId.cu]
        considering line: [  #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-9.res]
        considering line: [  #$ cicc --microsoft_version=1938 --msvc_target_version=1938 --compiler_bindir "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/../../../../../../.." --display_error_number --orig_src_file_name "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --orig_src_path_name "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" --allow_managed --debug_mode --sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10"  -arch compute_52 -m64 --no-version-ident -ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 --include_file_name "tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin.c" -g -O0 -tused --gen_module_id_file --module_id_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-5_CMakeCUDACompilerId.module_id" --gen_c_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.c" --stub_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.stub.c" --gen_device_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.gpu"  "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-8_CMakeCUDACompilerId.cpp1.ii" -o "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx"]
        considering line: [  #$ ptxas -arch=sm_52 -m64 -g --dont-merge-basicblocks --return-at-end "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx"  -o "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-10_CMakeCUDACompilerId.sm_52.cubin" ]
        considering line: [  #$ fatbinary -64 --ident="E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --cmdline="-g --dont-merge-basicblocks --return-at-end " --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " -g "--image3=kind=elf,sm=52,file=C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-10_CMakeCUDACompilerId.sm_52.cubin" "--image3=kind=ptx,sm=52,file=C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx" --embedded-fatbin="C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin.c" ]
        considering line: [  #$ erase C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin]
        considering line: [  #$ cl.exe -D__NV_NO_HOST_COMPILER_CHECK=1 @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-11.res" > "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-6_CMakeCUDACompilerId.cpp4.ii" ]
        considering line: [  CMakeCUDACompilerId.cu]
        considering line: [  #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-11.res]
        considering line: [  #$ cudafe++ --microsoft_version=1938 --msvc_target_version=1938 --compiler_bindir "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/../../../../../../.." --display_error_number --orig_src_file_name "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --orig_src_path_name "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" --allow_managed --debug_mode --sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10" --m64 --parse_templates --gen_c_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.cpp" --stub_file_name "tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.stub.c" --module_id_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-5_CMakeCUDACompilerId.module_id" "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-6_CMakeCUDACompilerId.cpp4.ii" ]
        considering line: [  #$ cl.exe @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-12.res" -Fo"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CompilerIdCUDA/x64/Debug/CMakeCUDACompilerId.cu.obj" ]
        considering line: [  tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.cpp]
        considering line: [  #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-12.res]
        considering line: [宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?CudaBuildCore 涓洰鏍?鐨勬搷浣溿€?]
        considering line: [Link:]
        considering line: [  C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj]
          extracted link line: [link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj]
        considering line: [    姝ｅ湪鍒涘缓搴?.\\CompilerIdCUDA.lib 鍜屽璞?.\\CompilerIdCUDA.exp]
        considering line: [LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj]]
        considering line: [  CompilerIdCUDA.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.exe]
        considering line: [PostBuildEvent:]
        considering line: [  echo CMAKE_CUDA_COMPILER=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe]
        considering line: [  :VCEnd]
        considering line: [  CMAKE_CUDA_COMPILER=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe]
        considering line: [FinalizeBuildStatus:]
        considering line: [  姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濄€?]
        considering line: [  姝ｅ湪瀵光€淒ebug\\CompilerIdCUDA.tlog\\CompilerIdCUDA.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?]
        considering line: [宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?]
        considering line: [宸叉垚鍔熺敓鎴愩€?]
        considering line: [鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣) (1) ->]
        considering line: [(Link 鐩爣) -> ]
        considering line: [  LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj]]
        considering line: [    1 涓鍛?]
        considering line: [    0 涓敊璇?]
        considering line: [宸茬敤鏃堕棿 00:00:04.36]
        considering line: []
      
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        link line: [cuda-fake-ld link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj]
          arg [cuda-fake-ld] ==> ignore
          arg [link.exe] ==> ignore
          arg [/ERRORREPORT:QUEUE] ==> ignore
          arg [/OUT:.\\CompilerIdCUDA.exe] ==> ignore
          arg [/INCREMENTAL:NO] ==> ignore
          arg [/NOLOGO] ==> ignore
          arg [/LIBPATH:C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64] ==> dir [C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64]
          arg [cudart_static.lib] ==> lib [cudart_static.lib]
          arg [/MANIFEST] ==> ignore
          arg [/MANIFESTUAC:level='asInvoker' uiAccess='false'] ==> ignore
          arg [/manifest:embed] ==> ignore
          arg [/PDB:.\\CompilerIdCUDA.pdb] ==> ignore
          arg [/SUBSYSTEM:CONSOLE] ==> ignore
          arg [/TLBID:1] ==> ignore
          arg [/DYNAMICBASE] ==> ignore
          arg [/NXCOMPAT] ==> ignore
          arg [/IMPLIB:.\\CompilerIdCUDA.lib] ==> ignore MSVC link option
          arg [/MACHINE:X64] ==> ignore
          arg [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj] ==> ignore
        collapse library dir [C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64] ==> [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64]
        implicit libs: [cudart_static.lib]
        implicit objs: []
        implicit dirs: [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/Internal/CMakeNVCCParseImplicitInfo.cmake:146 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCUDACompiler.cmake:242 (cmake_nvcc_parse_implicit_info)"
      - "CMakeLists.txt:46 (enable_language)"
    message: |
      Parsed CUDA nvcc include information:
        found 'PATH=' string: [C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../nvvm/bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../lib;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x64;;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x86;;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\ide;C:\\Program Files (x86)\\HTML Help Workshop;;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64;C:\\WINDOWS\\Microsoft.NET\\Framework64\\v4.0.30319\\;C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin;C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\;;D:\\miniconda;D:\\miniconda\\Library\\mingw-w64\\bin;D:\\miniconda\\Library\\usr\\bin;D:\\miniconda\\Library\\bin;D:\\miniconda\\Scripts;D:\\miniconda\\bin;D:\\miniconda\\condabin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\libnvvp;C:\\Python312\\Scripts;C:\\Python312;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Program Files\\dotnet;C:\\Program Files\\TortoiseSVN\\bin;C:\\Windows\\System32;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\TensorRT-*******\\lib;C:\\Program Files\\Docker\\Docker\\resources\\bin;D:\\Erlang OTP\\bin;D:\\ffmpeg-7.0.2-full_build\\bin;D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\VisualSVN Server\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;D:\\nc\\netcat-win32-1.11\\netcat-1.11;D:\\WezTerm;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\AppData\\Local\\Programs\\oh-my-posh\\bin;C:\\Users\\<USER>\\scoop\\shims;C:\\Program Files (x86)\\Microsoft\\Edge\\Application;C:\\Python312\\Scripts;C:\\Python312;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\libnvvp;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.1\\lib\\x64;C:\\Program Files\\dotnet;C:\\Program Files\\Git\\cmd;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Program Files\\TortoiseSVN\\bin;C:\\Windows\\System32;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\TensorRT-*******\\lib;C:\\Program Files\\Docker\\Docker\\resources\\bin;D:\\Erlang OTP\\bin;D:\\ffmpeg-7.0.2-full_build\\bin;D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\VisualSVN Server\\bin;\\bin\\x64-win64;C:\\Users\\<USER>\\AppData\\Local;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;D:\\cmake-3.31.6-windows-x86_64\\bin;C:\\Users\\<USER>\\xmake;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64;.;{03555C97-E719-40ed-B706-5CE607D0B817}_9832;.;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;]
        found 'LIBRARIES=' string: ["/LIBPATH:C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../lib/x64"]
        found 'INCLUDES=' string: ["-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin/../include"  ]
        considering line: [閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a]
        considering line: [鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:06銆?]
        considering line: [鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣)銆?]
        considering line: [PrepareForBuild:]
        considering line: [  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?]
        considering line: [  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?]
        considering line: [  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCUDA.tlog\\鈥濄€?]
        considering line: [InitializeBuildStatus:]
        considering line: [  姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?]
        considering line: [  姝ｅ湪瀵光€淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?]
        considering line: [AddCudaCompileDeps:]
        considering line: [  C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe /E /nologo /showIncludes /TP /D__CUDACC__ /D__CUDACC_VER_MAJOR__=11 /D__CUDACC_VER_MINOR__=8 /D_MBCS /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I. /FIcuda_runtime.h /c E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu ]
        considering line: [椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?1)姝ｅ湪鑺傜偣 1 涓婄敓鎴愨€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?1:2) (CudaBuildCore 涓洰鏍?銆?]
        considering line: [CudaBuildCore:]
        considering line: [  Compiling CUDA source file CMakeCUDACompilerId.cu...]
        considering line: [  姝ｅ湪鍒涘缓鐩綍鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug鈥濄€?]
        considering line: [  cmd.exe /C "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp8e13b2fa0d264f6185033debf32c38cd.cmd"]
        considering line: [  "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe" -gencode=arch=compute_52,code=\\"sm_52,compute_52\\" --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu   -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"  -G   --keep-dir CompilerIdCUDA\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -v -allow-unsupported-compiler -g  -D_MBCS -Xcompiler "/EHsc /W0 /nologo /Od /FdDebug\\vc143.pdb /FS /Zi /RTC1 /MDd " -o E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu"]
        considering line: [  ]
        considering line: [  E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA>"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe" -gencode=arch=compute_52,code=\\"sm_52,compute_52\\" --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu   -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"  -G   --keep-dir CompilerIdCUDA\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -v -allow-unsupported-compiler -g  -D_MBCS -Xcompiler "/EHsc /W0 /nologo /Od /FdDebug\\vc143.pdb /FS /Zi /RTC1 /MDd " -o E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" ]
        considering line: [                                  CMakeCUDACompilerId.cu]
        considering line: [  #$ cl.exe -D__NV_NO_HOST_COMPILER_CHECK=1 @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-9.res" > "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-8_CMakeCUDACompilerId.cpp1.ii" ]
        considering line: [  CMakeCUDACompilerId.cu]
        considering line: [  #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-9.res]
        considering line: [  #$ cicc --microsoft_version=1938 --msvc_target_version=1938 --compiler_bindir "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/../../../../../../.." --display_error_number --orig_src_file_name "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --orig_src_path_name "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" --allow_managed --debug_mode --sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10"  -arch compute_52 -m64 --no-version-ident -ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 --include_file_name "tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin.c" -g -O0 -tused --gen_module_id_file --module_id_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-5_CMakeCUDACompilerId.module_id" --gen_c_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.c" --stub_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.stub.c" --gen_device_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.gpu"  "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-8_CMakeCUDACompilerId.cpp1.ii" -o "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx"]
        considering line: [  #$ ptxas -arch=sm_52 -m64 -g --dont-merge-basicblocks --return-at-end "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx"  -o "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-10_CMakeCUDACompilerId.sm_52.cubin" ]
        considering line: [  #$ fatbinary -64 --ident="E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --cmdline="-g --dont-merge-basicblocks --return-at-end " --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " -g "--image3=kind=elf,sm=52,file=C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-10_CMakeCUDACompilerId.sm_52.cubin" "--image3=kind=ptx,sm=52,file=C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.ptx" --embedded-fatbin="C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin.c" ]
        considering line: [  #$ erase C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-4_CMakeCUDACompilerId.fatbin]
        considering line: [  #$ cl.exe -D__NV_NO_HOST_COMPILER_CHECK=1 @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-11.res" > "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-6_CMakeCUDACompilerId.cpp4.ii" ]
        considering line: [  CMakeCUDACompilerId.cu]
        considering line: [  #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-11.res]
        considering line: [  #$ cudafe++ --microsoft_version=1938 --msvc_target_version=1938 --compiler_bindir "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/../../../../../../.." --display_error_number --orig_src_file_name "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CMakeCUDACompilerId.cu" --orig_src_path_name "E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CMakeCUDACompilerId.cu" --allow_managed --debug_mode --sdk_dir "C:\\Program Files (x86)\\Windows Kits\\10" --m64 --parse_templates --gen_c_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.cpp" --stub_file_name "tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.stub.c" --module_id_file_name "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-5_CMakeCUDACompilerId.module_id" "C:/Users/<USER>/AppData/Local/Temp/tmpxft_00003da0_00000000-6_CMakeCUDACompilerId.cpp4.ii" ]
        considering line: [  #$ cl.exe @"C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-12.res" -Fo"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/3.31.6/CompilerIdCUDA/CompilerIdCUDA/x64/Debug/CMakeCUDACompilerId.cu.obj" ]
        considering line: [  tmpxft_00003da0_00000000-7_CMakeCUDACompilerId.cudafe1.cpp]
        considering line: [  #$ erase C:\\Users\\<USER>\\AppData\\Local\\Temp/tmpxft_00003da0_00000000-12.res]
        considering line: [宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?CudaBuildCore 涓洰鏍?鐨勬搷浣溿€?]
        considering line: [Link:]
        considering line: [  C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj]
          extracted link line: [link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj]
        considering line: [    姝ｅ湪鍒涘缓搴?.\\CompilerIdCUDA.lib 鍜屽璞?.\\CompilerIdCUDA.exp]
        considering line: [LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj]]
        considering line: [  CompilerIdCUDA.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.exe]
        considering line: [PostBuildEvent:]
        considering line: [  echo CMAKE_CUDA_COMPILER=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe]
        considering line: [  :VCEnd]
        considering line: [  CMAKE_CUDA_COMPILER=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe]
        considering line: [FinalizeBuildStatus:]
        considering line: [  姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCUDA.tlog\\unsuccessfulbuild鈥濄€?]
        considering line: [  姝ｅ湪瀵光€淒ebug\\CompilerIdCUDA.tlog\\CompilerIdCUDA.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?]
        considering line: [宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?]
        considering line: [宸叉垚鍔熺敓鎴愩€?]
        considering line: [鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj鈥?榛樿鐩爣) (1) ->]
        considering line: [(Link 鐩爣) -> ]
        considering line: [  LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA.vcxproj]]
        considering line: [    1 涓鍛?]
        considering line: [    0 涓敊璇?]
        considering line: [宸茬敤鏃堕棿 00:00:04.36]
        considering line: []
      
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        link line: [cuda-fake-ld link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCUDA.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCUDA.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCUDA.lib" /MACHINE:X64 E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj]
          arg [cuda-fake-ld] ==> ignore
          arg [link.exe] ==> ignore
          arg [/ERRORREPORT:QUEUE] ==> ignore
          arg [/OUT:.\\CompilerIdCUDA.exe] ==> ignore
          arg [/INCREMENTAL:NO] ==> ignore
          arg [/NOLOGO] ==> ignore
          arg [/LIBPATH:C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64] ==> dir [C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64]
          arg [cudart_static.lib] ==> lib [cudart_static.lib]
          arg [/MANIFEST] ==> ignore
          arg [/MANIFESTUAC:level='asInvoker' uiAccess='false'] ==> ignore
          arg [/manifest:embed] ==> ignore
          arg [/PDB:.\\CompilerIdCUDA.pdb] ==> ignore
          arg [/SUBSYSTEM:CONSOLE] ==> ignore
          arg [/TLBID:1] ==> ignore
          arg [/DYNAMICBASE] ==> ignore
          arg [/NXCOMPAT] ==> ignore
          arg [/IMPLIB:.\\CompilerIdCUDA.lib] ==> ignore MSVC link option
          arg [/MACHINE:X64] ==> ignore
          arg [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\3.31.6\\CompilerIdCUDA\\CompilerIdCUDA\\x64\\Debug\\CMakeCUDACompilerId.cu.obj] ==> ignore
        collapse library dir [C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64] ==> [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64]
        implicit libs: [cudart_static.lib]
        implicit objs: []
        implicit dirs: [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCUDACompiler.cmake:19 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:46 (enable_language)"
    checks:
      - "Detecting CUDA compiler ABI info"
    directories:
      source: "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz"
      binary: "E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz"
    cmakeVariables:
      CMAKE_CUDA_FLAGS: "-D_WINDOWS -Xcompiler=\" /GR /EHsc\""
      CMAKE_CUDA_FLAGS_DEBUG: "-Xcompiler=\" -Zi -Ob0 -Od /RTC1\""
      CMAKE_CUDA_RUNTIME_LIBRARY: "Static"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CUDA_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_07e7d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:10銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_07e7d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        AddCudaCompileDeps:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe /E /nologo /showIncludes /TP /D__CUDACC__ /D__CUDACC_VER_MAJOR__=11 /D__CUDACC_VER_MINOR__=8 /D_WINDOWS /DCMAKE_INTDIR="Debug" /D_MBCS /DCMAKE_INTDIR="Debug" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I. /FIcuda_runtime.h /c D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu 
        椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?1)姝ｅ湪鑺傜偣 1 涓婄敓鎴愨€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?1:2) (CudaBuildCore 涓洰鏍?銆?
        CudaBuildCore:
          Compiling CUDA source file D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu...
          cmd.exe /C "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp09803dd4bdef4c80ada694ae488aa89a.cmd"
          "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe"  --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu    -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"     --keep-dir cmTC_07e7d\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -Xcompiler="/EHsc -Zi -Ob0 -v" -g  -D_WINDOWS -D"CMAKE_INTDIR=\\"Debug\\"" -D_MBCS -D"CMAKE_INTDIR=\\"Debug\\"" -Xcompiler "/EHsc /W1 /nologo /Od /FdcmTC_07e7d.dir\\Debug\\vc143.pdb /FS /Zi /RTC1 /MDd /GR" -o cmTC_07e7d.dir\\Debug\\CMakeCUDACompilerABI.obj "D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu"
          
          E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz>"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe"  --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu    -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"     --keep-dir cmTC_07e7d\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -Xcompiler="/EHsc -Zi -Ob0 -v" -g  -D_WINDOWS -D"CMAKE_INTDIR=\\"Debug\\"" -D_MBCS -D"CMAKE_INTDIR=\\"Debug\\"" -Xcompiler "/EHsc /W1 /nologo /Od /FdcmTC_07e7d.dir\\Debug\\vc143.pdb /FS /Zi /RTC1 /MDd /GR" -o cmTC_07e7d.dir\\Debug\\CMakeCUDACompilerABI.obj "D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu" 
        cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          CMakeCUDACompilerABI.cu
        cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          CMakeCUDACompilerABI.cu
          CMakeCUDACompilerABI.cu
        cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
        D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCompilerCUDAArch.h(10): warning C4305: 鈥渞eturn鈥? 浠庘€渋nt鈥濆埌鈥渂ool鈥濇埅鏂?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?CudaBuildCore 涓洰鏍?鐨勬搷浣溿€?
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\Debug\\cmTC_07e7d.exe" /INCREMENTAL /ILK:"cmTC_07e7d.dir\\Debug\\cmTC_07e7d.ilk" /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudadevrt.lib cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.lib" /MACHINE:X64  /machine:x64 -v cmTC_07e7d.dir\\Debug\\CMakeCUDACompilerABI.obj
        LINK : warning LNK4044: 鏃犳硶璇嗗埆鐨勯€夐」鈥?v鈥濓紱宸插拷鐣?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
            姝ｅ湪鍒涘缓搴?E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.lib 鍜屽璞?E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.exp
        LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          cmTC_07e7d.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\Debug\\cmTC_07e7d.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\cmTC_07e7d.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
        
        鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣) (1) ->
        鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?CudaBuildCore 鐩爣) (1:2) ->
        (CudaBuildCore 鐩爣) -> 
          cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCompilerCUDAArch.h(10): warning C4305: 鈥渞eturn鈥? 浠庘€渋nt鈥濆埌鈥渂ool鈥濇埅鏂?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
        
        
        鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : warning LNK4044: 鏃犳硶璇嗗埆鐨勯€夐」鈥?v鈥濓紱宸插拷鐣?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
          LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]
        
            6 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:03.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCUDACompiler.cmake:19 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:46 (enable_language)"
    message: |
      Parsed CUDA implicit include dir info: rv=start
        warn: unable to parse implicit include dirs!
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCUDACompiler.cmake:19 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:46 (enable_language)"
    message: |
      Parsed CUDA implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        ignore line: [Change Dir: 'E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_07e7d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n]
        ignore line: [閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a]
        ignore line: [鐢熸垚鍚姩鏃堕棿涓?2025/6/19 21:31:10銆?]
        ignore line: []
        ignore line: [鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣)銆?]
        ignore line: [PrepareForBuild:]
        ignore line: [  姝ｅ湪鍒涘缓鐩綍鈥渃mTC_07e7d.dir\\Debug\\鈥濄€?]
        ignore line: [  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?]
        ignore line: [  姝ｅ湪鍒涘缓鐩綍鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\Debug\\鈥濄€?]
        ignore line: [  姝ｅ湪鍒涘缓鐩綍鈥渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\鈥濄€?]
        ignore line: [InitializeBuildStatus:]
        ignore line: [  姝ｅ湪鍒涘缓鈥渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?]
        ignore line: [  姝ｅ湪瀵光€渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?]
        ignore line: [AddCudaCompileDeps:]
        ignore line: [  C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe /E /nologo /showIncludes /TP /D__CUDACC__ /D__CUDACC_VER_MAJOR__=11 /D__CUDACC_VER_MINOR__=8 /D_WINDOWS /DCMAKE_INTDIR="Debug" /D_MBCS /DCMAKE_INTDIR="Debug" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin" /I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include" /I. /FIcuda_runtime.h /c D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu ]
        ignore line: [椤圭洰鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?1)姝ｅ湪鑺傜偣 1 涓婄敓鎴愨€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?1:2) (CudaBuildCore 涓洰鏍?銆?]
        ignore line: [CudaBuildCore:]
        ignore line: [  Compiling CUDA source file D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu...]
        ignore line: [  cmd.exe /C "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp09803dd4bdef4c80ada694ae488aa89a.cmd"]
        ignore line: [  "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe"  --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu    -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"     --keep-dir cmTC_07e7d\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -Xcompiler="/EHsc -Zi -Ob0 -v" -g  -D_WINDOWS -D"CMAKE_INTDIR=\\"Debug\\"" -D_MBCS -D"CMAKE_INTDIR=\\"Debug\\"" -Xcompiler "/EHsc /W1 /nologo /Od /FdcmTC_07e7d.dir\\Debug\\vc143.pdb /FS /Zi /RTC1 /MDd /GR" -o cmTC_07e7d.dir\\Debug\\CMakeCUDACompilerABI.obj "D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu"]
        ignore line: [  ]
        ignore line: [  E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz>"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin\\nvcc.exe"  --use-local-env -ccbin "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64" -x cu    -I"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\include"     --keep-dir cmTC_07e7d\\x64\\Debug  -maxrregcount=0  --machine 64 --compile -cudart static -Xcompiler="/EHsc -Zi -Ob0 -v" -g  -D_WINDOWS -D"CMAKE_INTDIR=\\"Debug\\"" -D_MBCS -D"CMAKE_INTDIR=\\"Debug\\"" -Xcompiler "/EHsc /W1 /nologo /Od /FdcmTC_07e7d.dir\\Debug\\vc143.pdb /FS /Zi /RTC1 /MDd /GR" -o cmTC_07e7d.dir\\Debug\\CMakeCUDACompilerABI.obj "D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCUDACompilerABI.cu" ]
        ignore line: [cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  CMakeCUDACompilerABI.cu]
        ignore line: [cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  CMakeCUDACompilerABI.cu]
        ignore line: [  CMakeCUDACompilerABI.cu]
        ignore line: [cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCompilerCUDAArch.h(10): warning C4305: 鈥渞eturn鈥? 浠庘€渋nt鈥濆埌鈥渂ool鈥濇埅鏂?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?CudaBuildCore 涓洰鏍?鐨勬搷浣溿€?]
        ignore line: [Link:]
        ignore line: [  C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\Debug\\cmTC_07e7d.exe" /INCREMENTAL /ILK:"cmTC_07e7d.dir\\Debug\\cmTC_07e7d.ilk" /NOLOGO /LIBPATH:"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\lib\\x64" cudadevrt.lib cudart_static.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.lib" /MACHINE:X64  /machine:x64 -v cmTC_07e7d.dir\\Debug\\CMakeCUDACompilerABI.obj]
        ignore line: [LINK : warning LNK4044: 鏃犳硶璇嗗埆鐨勯€夐」鈥?v鈥濓紱宸插拷鐣?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [    姝ｅ湪鍒涘缓搴?E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.lib 鍜屽璞?E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/CMakeScratch/TryCompile-hbz1hz/Debug/cmTC_07e7d.exp]
        ignore line: [LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  cmTC_07e7d.vcxproj -> E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\Debug\\cmTC_07e7d.exe]
        ignore line: [FinalizeBuildStatus:]
        ignore line: [  姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\unsuccessfulbuild鈥濄€?]
        ignore line: [  姝ｅ湪瀵光€渃mTC_07e7d.dir\\Debug\\cmTC_07e7d.tlog\\cmTC_07e7d.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?]
        ignore line: [宸插畬鎴愮敓鎴愰」鐩€淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?]
        ignore line: []
        ignore line: [宸叉垚鍔熺敓鎴愩€?]
        ignore line: []
        ignore line: [鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣) (1) ->]
        ignore line: [鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?CudaBuildCore 鐩爣) (1:2) ->]
        ignore line: [(CudaBuildCore 鐩爣) -> ]
        ignore line: [  cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  cl : 鍛戒护琛?warning D9002: 蹇界暐鏈煡閫夐」鈥?v鈥?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  D:\\cmake-3.31.6-windows-x86_64\\share\\cmake-3.31\\Modules\\CMakeCompilerCUDAArch.h(10): warning C4305: 鈥渞eturn鈥? 浠庘€渋nt鈥濆埌鈥渂ool鈥濇埅鏂?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: []
        ignore line: []
        ignore line: [鈥淓:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj鈥?榛樿鐩爣) (1) ->]
        ignore line: [(Link 鐩爣) -> ]
        ignore line: [  LINK : warning LNK4044: 鏃犳硶璇嗗埆鐨勯€夐」鈥?v鈥濓紱宸插拷鐣?[E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: [  LINK : warning LNK4098: 榛樿搴撯€淟IBCMT鈥濅笌鍏朵粬搴撶殑浣跨敤鍐茬獊锛涜浣跨敤 /NODEFAULTLIB:library [E:\\study\\AI_deploy\\TensorRT\\yolov8_seg\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hbz1hz\\cmTC_07e7d.vcxproj]]
        ignore line: []
        ignore line: [    6 涓鍛?]
        ignore line: [    0 涓敊璇?]
        ignore line: []
        ignore line: [宸茬敤鏃堕棿 00:00:03.62]
        ignore line: []
        ignore line: []
        linker tool for 'CUDA': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31/Modules/CMakeTestCUDACompiler.cmake:19 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:46 (enable_language)"
    message: |
      Running the CUDA compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.38.33145.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
