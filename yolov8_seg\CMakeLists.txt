cmake_minimum_required(VERSION 3.16)
project(yolov8_seg VERSION 1.0.0 LANGUAGES CXX)

# C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Compiler options
if(MSVC)
    add_compile_options(/W3 /MP)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS NOMINMAX)
else()
    add_compile_options(-Wall -Wextra)
endif()

# Find OpenCV
if(WIN32)
    # Common OpenCV installation paths on Windows
    set(OpenCV_SEARCH_PATHS
        "D:/opencv/build"
        "C:/opencv/build"
        "C:/Program Files/opencv/build"
        "$ENV{OPENCV_DIR}"
        "$ENV{OpenCV_DIR}"
    )

    foreach(path ${OpenCV_SEARCH_PATHS})
        if(EXISTS "${path}/OpenCVConfig.cmake" OR EXISTS "${path}/opencv-config.cmake")
            set(OpenCV_DIR "${path}")
            break()
        endif()
    endforeach()
endif()

find_package(OpenCV REQUIRED)

# Find CUDA
enable_language(CUDA)
find_package(CUDAToolkit REQUIRED)

# Find TensorRT
find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    HINTS
        $ENV{TENSORRT_ROOT}/include  # 编译的时候临时加入环境变量  如下注释
        /usr/include/x86_64-linux-gnu
        /usr/local/include
        /opt/tensorrt/include
    PATHS
        "C:/TensorRT*/include"
        "/usr/include/tensorrt"
)

# # Windows
# set TENSORRT_ROOT=C:\TensorRT-8.6.1.6

# # Linux
# export TENSORRT_ROOT=/opt/tensorrt

find_library(TENSORRT_LIB nvinfer
    HINTS
        $ENV{TENSORRT_ROOT}/lib
        /usr/lib/x86_64-linux-gnu
        /usr/local/lib
        /opt/tensorrt/lib
    PATHS
        "C:/TensorRT*/lib"
        "/usr/lib/tensorrt"
)

find_library(TENSORRT_PARSER_LIB nvonnxparser
    HINTS
        $ENV{TENSORRT_ROOT}/lib
        /usr/lib/x86_64-linux-gnu
        /usr/local/lib
        /opt/tensorrt/lib
    PATHS
        "C:/TensorRT*/lib"
        "/usr/lib/tensorrt"
)

find_library(TENSORRT_PLUGIN_LIB nvinfer_plugin
    HINTS
        $ENV{TENSORRT_ROOT}/lib
        /usr/lib/x86_64-linux-gnu
        /usr/local/lib
        /opt/tensorrt/lib
    PATHS
        "C:/TensorRT*/lib"
        "/usr/lib/tensorrt"
)

# Verify TensorRT
if(NOT TENSORRT_INCLUDE_DIR OR NOT TENSORRT_LIB)
    message(FATAL_ERROR "TensorRT not found. Please set TENSORRT_ROOT environment variable or install TensorRT in standard locations.")
endif()

# Create executable
add_executable(${PROJECT_NAME}
    src/main.cpp
    src/yolov8_seg_trt.cpp
)

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE
    ${OpenCV_INCLUDE_DIRS}
    ${TENSORRT_INCLUDE_DIR}
    ${CMAKE_SOURCE_DIR}/include
)

# Link libraries
target_link_libraries(${PROJECT_NAME} PRIVATE
    ${OpenCV_LIBS}
    ${TENSORRT_LIB}
    ${TENSORRT_PARSER_LIB}
    ${TENSORRT_PLUGIN_LIB}
    CUDA::cudart
)

# Copy runtime dependencies (Windows only)
if(WIN32)
    # Function to copy DLLs
    function(copy_dlls target dll_dir pattern)
        if(EXISTS ${dll_dir})
            file(GLOB dlls "${dll_dir}/${pattern}")
            foreach(dll ${dlls})
                add_custom_command(TARGET ${target} POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_if_different
                    ${dll} $<TARGET_FILE_DIR:${target}>
                    COMMENT "Copying ${dll}"
                )
            endforeach()
        endif()
    endfunction()

    # Copy OpenCV DLLs
    # Try to find OpenCV bin directory
    set(OPENCV_BIN_DIRS
        "${OpenCV_DIR}/x64/vc15/bin"
        "${OpenCV_DIR}/x64/vc16/bin"
        "${OpenCV_DIR}/bin"
        "${OpenCV_DIR}/../bin"
    )

    foreach(bin_dir ${OPENCV_BIN_DIRS})
        if(EXISTS ${bin_dir})
            copy_dlls(${PROJECT_NAME} ${bin_dir} "opencv_*.dll")
            message(STATUS "Found OpenCV DLLs in: ${bin_dir}")
            break()
        endif()
    endforeach()

    # Copy TensorRT DLLs
    if(TENSORRT_LIB)
        get_filename_component(TENSORRT_LIB_DIR ${TENSORRT_LIB} DIRECTORY)
        copy_dlls(${PROJECT_NAME} ${TENSORRT_LIB_DIR} "*.dll")
    endif()

    # Copy CUDA DLLs
    copy_dlls(${PROJECT_NAME} ${CUDAToolkit_BIN_DIR} "cudart*.dll")
endif()

# Print configuration
message(STATUS "=== Configuration Summary ===")
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "TensorRT include: ${TENSORRT_INCLUDE_DIR}")
message(STATUS "TensorRT library: ${TENSORRT_LIB}")
message(STATUS "CUDA version: ${CUDAToolkit_VERSION}")
