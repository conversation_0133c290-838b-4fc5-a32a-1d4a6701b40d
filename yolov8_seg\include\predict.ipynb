{"cells": [{"cell_type": "code", "execution_count": null, "id": "54d8eafb", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["fffff"]}, {"cell_type": "code", "execution_count": null, "id": "d7c0cf55", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ac32b9c4", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6a58f623", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}